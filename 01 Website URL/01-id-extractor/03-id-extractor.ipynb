{"cells": [{"cell_type": "code", "execution_count": null, "id": "32d55272", "metadata": {}, "outputs": [], "source": ["from playwright.async_api import async_playwright\n", "from parsel import Selector\n", "import csv\n", "import requests\n", "import json\n", "import time\n", "import random, asyncio\n", "from playwright_stealth import Stealth\n", "import re\n", "import os\n", "\n", "INPUT_FILE = \"extrait_10001_20001_part_3.csv\"\n", "OUTPUT_FILE = \"extrait_10001_20001_part_3_output_id.csv\"\n", "\n", "RAW_NUMBER = 0\n", "SKIP_RAW = 0  # Start from beginning"]}, {"cell_type": "code", "execution_count": null, "id": "042070cc", "metadata": {}, "outputs": [], "source": ["USER_DATA_DIR = os.path.join(os.getcwd(), \"login_files_3\")\n", "playwright = await async_playwright().start()\n", "\n", "context = await playwright.chromium.launch_persistent_context(\n", "    user_data_dir=USER_DATA_DIR,\n", "            headless=False,\n", "            channel=\"chrome\",\n", "            args=[\n", "                \"--disable-blink-features=AutomationControlled\",\n", "                \"--exclude-switches=enable-automation\",\n", "                \"--disable-extensions\",\n", "                \"--disable-plugins-discovery\", \n", "                \"--disable-default-apps\",\n", "                \"--no-first-run\",\n", "                \"--disable-client-side-phishing-detection\",\n", "                \"--disable-popup-blocking\",\n", "                \"--disable-hang-monitor\",\n", "                \"--disable-sync\",\n", "                \"--no-report-upload\",\n", "                \"--disable-features=TranslateUI,VizDisplayCompositor\",\n", "                \"--disable-infobars\",\n", "                \"--disable-notifications\"\n", "            ],\n", "            user_agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) \"\n", "                       \"AppleWebKit/537.36 (KHTML, like Gecko) \"\n", "                       \"Chrome/********* Safari/537.36\",\n", "            locale=\"en-US\",\n", "            timezone_id=\"America/New_York\",\n", "            permissions=[\"geolocation\"],\n", "            viewport={\"width\":1366, \"height\":768},\n", "            java_script_enabled=True,  # CRITICAL: Must be True for stealth to work\n", "            ignore_default_args=[\n", "                \"--enable-automation\", \n", "                \"--enable-blink-features=AutomationControlled\",\n", "                \"--no-sandbox\"\n", "            ]\n", "        )\n", "\n", "# Advanced anti-detection scripts\n", "await context.add_init_script(\"\"\"\n", "    // Remove webdriver property\n", "    Object.defineProperty(navigator, 'webdriver', {get: () => undefined});\n", "    \n", "    // Override plugins\n", "    Object.defineProperty(navigator, 'plugins', {\n", "        get: () => [1, 2, 3, 4, 5]\n", "    });\n", "    \n", "    // Override languages\n", "    Object.defineProperty(navigator, 'languages', {\n", "        get: () => ['en-US', 'en']\n", "    });\n", "    \n", "    // Override chrome property\n", "    window.chrome = {\n", "        runtime: {},\n", "        loadTimes: function() {},\n", "        csi: function() {},\n", "        app: {}\n", "    };\n", "    \n", "    // Override permissions\n", "    const originalQuery = window.navigator.permissions.query;\n", "    window.navigator.permissions.query = (parameters) => (\n", "        parameters.name === 'notifications' ?\n", "            Promise.resolve({ state: Notification.permission }) :\n", "            originalQuery(parameters)\n", "    );\n", "    \n", "    // Remove automation indicators\n", "    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;\n", "    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;\n", "    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;\n", "\"\"\")\n", "\n", "page = await context.new_page()\n", "\n", "# Apply stealth configuration \n", "stealth = Stealth()\n", "await stealth.apply_stealth_async(page)"]}, {"cell_type": "code", "execution_count": null, "id": "7b27b8a4", "metadata": {}, "outputs": [], "source": ["# Read the CSV file\n", "data_rows = []\n", "\n", "with open(INPUT_FILE, 'r', encoding='utf-8') as file:\n", "    csv_reader = csv.DictReader(file)\n", "    headers = csv_reader.fieldnames\n", "    \n", "    for row in csv_reader:\n", "        data_rows.append(row)"]}, {"cell_type": "code", "execution_count": null, "id": "5421a1d2", "metadata": {}, "outputs": [], "source": ["await page.goto(\"https://www.verif.com/back-api/search?query=*********&isoCode=FR&resultType=organization&pageNumber=1&sortOrder=score&locale=fr\")"]}, {"cell_type": "code", "execution_count": null, "id": "1f79f6ce", "metadata": {}, "outputs": [], "source": ["# Prepare CSV file with headers (create/overwrite the file)\n", "original_fieldnames = list(data_rows[0].keys()) if data_rows else []\n", "new_fieldnames = ['_id', 'company_name_original', 'operating_status_description', 'verif_url']\n", "all_fieldnames = original_fieldnames + new_fieldnames\n", "\n", "# Create CSV file and write header\n", "with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "    writer.writeheader()\n", "\n", "print(f\"Created {OUTPUT_FILE} with headers. Starting real-time processing...\")\n", "\n", "# Process each row and save immediately\n", "processed_count = 0\n", "for index, row in enumerate(data_rows):\n", "    if index < SKIP_RAW:\n", "        continue\n", "\n", "    siren = str(row['siren'])\n", "    current_row = index + 1\n", "    \n", "    # Dynamic progress display\n", "    print(f\"Processing Raw Number: {RAW_NUMBER} (SIREN: {siren})...\")\n", "    RAW_NUMBER += 1\n", "    \n", "\n", "    # Initialize default values\n", "    _id = None\n", "    company_name_original = None\n", "    operating_status_description = None\n", "    verif_url = None\n", "    \n", "    try:\n", "        URL = f\"https://www.verif.com/back-api/search?query={siren}&isoCode=FR&resultType=organization&pageNumber=1&sortOrder=score&locale=fr\"\n", "\n", "        await page.goto(URL)\n", "        await page.wait_for_timeout(300)\n", "        \n", "        # Get HTML content and extract JSON\n", "        html = await page.content()\n", "        \n", "        # Extract JSON from the HTML\n", "        json_pattern = r'<pre>(.*?)</pre>'\n", "        json_match = re.search(json_pattern, html, re.DOTALL)\n", "\n", "        if json_match:\n", "            json_str = json_match.group(1)\n", "            data = json.loads(json_str)\n", "            \n", "            # Extract company information if hits exist\n", "            if data.get('hits') and len(data['hits']) > 0:\n", "                company = data['hits'][0]  # Get first result\n", "                \n", "                _id = company.get('_id')\n", "                company_name_original = company.get('companyNameOriginal')\n", "                operating_status_description = company.get('operatingStatusDescription')\n", "                verif_url = f\"https://www.verif.com/societe/{_id}/\" if _id else None\n", "                   \n", "        else:\n", "            print(f\"  No JSON data found for SIREN: {siren}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"  Error processing SIREN {siren}: {str(e)}\")\n", "    \n", "    # Prepare the result row (combining original row data with new extracted data)\n", "    result_row = row.copy()\n", "    result_row.update({\n", "        '_id': _id,\n", "        'company_name_original': company_name_original,\n", "        'operating_status_description': operating_status_description,\n", "        'verif_url': verif_url\n", "    })\n", "    \n", "    # Save immediately to CSV file (append mode)\n", "    try:\n", "        with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:\n", "            writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "            writer.writerow(result_row)\n", "        processed_count += 1\n", "        print(f\"  ✓ Saved to CSV (Total saved: {processed_count})\")\n", "    except Exception as e:\n", "        print(f\"  ✗ Error saving to CSV: {str(e)}\")\n", "    \n", "    # Add small delay between requests\n", "    await page.wait_for_timeout(random.randint(100, 500))\n", "\n", "print(f\"\\nProcessing complete! Processed and saved {processed_count} records to {OUTPUT_FILE}.\")\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "48d81adc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8653a264", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25dd1161", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a84221d5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fa42fdd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4fdc5441", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "aea4e5b9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc59a5ce", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}