{"cells": [{"cell_type": "code", "execution_count": null, "id": "a067cfa6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "30ebdaad", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "11d792cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cdd0dbe7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "65713586", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "141b80d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "81bc124f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "99cc9bb3", "metadata": {}, "outputs": [], "source": ["from playwright.async_api import async_playwright\n", "from parsel import Selector\n", "import csv\n", "import requests\n", "import json\n", "import time\n", "import random, asyncio\n", "from playwright_stealth import Stealth\n", "\n", "\n", "playwright = await async_playwright().start()\n", "browser = await playwright.chromium.launch(\n", "            headless=False,\n", "            channel=\"chrome\",\n", "            args=[\"--disable-blink-features\", \"--disable-blink-features=AutomationControlled\"]\n", "        )\n", "\n", "\n", "context = await browser.new_context(\n", "            user_agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) \"\n", "                       \"AppleWebKit/537.36 (KHTML, like Gecko) \"\n", "                       \"Chrome/********* Safari/537.36\",\n", "            locale=\"en-US\",\n", "            timezone_id=\"America/New_York\",\n", "            permissions=[\"geolocation\"],\n", "            viewport={\"width\":1366, \"height\":768},\n", "            # java_script_enabled=False\n", "        )\n", "\n", "await context.add_init_script(\"Object.defineProperty(navigator,'webdriver',{get:()=>undefined});\")\n", "page = await context.new_page()\n", "\n", "# Apply stealth configuration\n", "stealth = Stealth()\n", "await stealth.apply_stealth_async(page)"]}, {"cell_type": "code", "execution_count": null, "id": "aa3c1597", "metadata": {}, "outputs": [], "source": ["https://www.verif.com/societe/d411c14dbe031a97702e7692f723f0d36aef779c679eb7e56761b44a2cc38467/"]}, {"cell_type": "code", "execution_count": null, "id": "00f13e45", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d83695b2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1931d122", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}