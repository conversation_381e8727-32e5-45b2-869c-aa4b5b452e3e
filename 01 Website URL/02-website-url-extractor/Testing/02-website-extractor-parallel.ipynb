{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "import csv\n", "import time\n", "import threading\n", "from urllib.parse import urlparse\n", "from parsel import Selector\n", "import undetected_chromedriver as uc\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "from selenium.common.exceptions import TimeoutException\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "from queue import Queue\n", "\n", "# Configuration\n", "INPUT_FILE = \"extrait_10001_20001_part_2_output_id.csv\"\n", "OUTPUT_FILE = \"extrait_10001_20001_part_2_output_url_parallel.csv\"\n", "user_data_dir = os.path.join(os.getcwd(), \"verif_login\")\n", "MAX_TABS = 2  # Number of tabs to run simultaneously in one browser\n", "REQUESTS_PER_BROWSER = 10  # Close browser after this many requests\n", "SKIP_ROWS = 0  # Skip first N rows if needed\n", "\n", "# Global variables for tracking\n", "processed_count = 0\n", "csv_lock = threading.Lock()\n", "progress_lock = threading.Lock()\n", "browser_lock = threading.Lock()\n", "total_requests = 0\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STEP 1: Setup initial browser for manual login and configuration\n", "print(\"Setting up initial browser for manual configuration...\")\n", "\n", "# Create Chrome options for setup browser\n", "chrome_options = uc.ChromeOptions()\n", "chrome_options.add_argument(f\"--user-data-dir={user_data_dir}\")\n", "\n", "# Add Chrome options for better compatibility\n", "chrome_options.add_argument('--no-sandbox')\n", "chrome_options.add_argument('--disable-extensions')\n", "chrome_options.add_argument('--ignore-certificate-errors')\n", "chrome_options.add_argument('--disable-blink-features=AutomationControlled')\n", "chrome_options.add_argument('--disable-infobars')\n", "chrome_options.add_argument('--disable-dev-shm-usage')\n", "chrome_options.add_argument('--disable-browser-side-navigation')\n", "chrome_options.add_argument('--disable-gpu')\n", "chrome_options.add_argument('--window-size=1200,800')  # Larger window for manual use\n", "\n", "# Create the setup browser instance\n", "setup_driver = uc.Chrome(options=chrome_options)\n", "setup_driver.set_page_load_timeout(15)\n", "\n", "# Navigate to test page\n", "test_url = \"https://www.verif.com/\"\n", "setup_driver.get(test_url)\n", "\n", "print(\"✅ Setup browser opened!\")\n", "print(\"👆 Please manually:\")\n", "print(\"   1. <PERSON>gin to your account\")\n", "print(\"   2. Accept cookies\")\n", "print(\"   3. Configure any settings needed\")\n", "print(\"   4. When done, close this browser manually\")\n", "print(\"   5. Then run the next cell to start parallel processing\")\n", "\n", "# Note: The user will manually close this browser when done with setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STEP 2: Read the CSV file with URLs\n", "print(f\"Reading data from {INPUT_FILE}...\")\n", "\n", "# Load all rows from CSV\n", "data_rows = []\n", "with open(INPUT_FILE, 'r', encoding='utf-8') as file:\n", "    csv_reader = csv.DictReader(file)\n", "    headers = csv_reader.fieldnames\n", "    \n", "    for row in csv_reader:\n", "        data_rows.append(row)\n", "\n", "print(f\"✅ Loaded {len(data_rows)} rows from {INPUT_FILE}\")\n", "\n", "# Apply skip if needed\n", "if SKIP_ROWS > 0:\n", "    data_rows = data_rows[SKIP_ROWS:]\n", "    print(f\"📋 Processing {len(data_rows)} rows (skipped first {SKIP_ROWS})\")\n", "else:\n", "    print(f\"📋 Processing all {len(data_rows)} rows\")\n", "\n", "# Prepare output CSV headers\n", "original_fieldnames = list(headers) if headers else []\n", "new_fieldnames = ['website_url', 'siren_number']\n", "all_fieldnames = original_fieldnames + new_fieldnames\n", "\n", "print(f\"📄 Output file: {OUTPUT_FILE}\")\n", "print(f\"🔧 Columns to add: {new_fieldnames}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STEP 3: Create output CSV file with headers\n", "print(\"Setting up output CSV file...\")\n", "\n", "# Create/overwrite output file with headers\n", "with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "    writer.writeheader()\n", "\n", "print(f\"✅ Output CSV file created: {OUTPUT_FILE}\")\n", "\n", "# Initialize browser and tab tracking\n", "main_browser = None  # Single browser instance\n", "tab_handles = []  # List of tab handles\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STEP 4: Function to create single browser with multiple tabs\n", "def create_browser_with_tabs():\n", "    global main_browser, tab_handles, total_requests\n", "    \n", "    print(f\"  🆕 Creating browser with {MAX_TABS} tabs\")\n", "    \n", "    # Create Chrome options\n", "    chrome_options = uc.ChromeOptions()\n", "    chrome_options.add_argument(f\"--user-data-dir={user_data_dir}\")\n", "    \n", "    # Add Chrome options for automation\n", "    chrome_options.add_argument('--no-sandbox')\n", "    chrome_options.add_argument('--disable-extensions')\n", "    chrome_options.add_argument('--ignore-certificate-errors')\n", "    chrome_options.add_argument('--disable-blink-features=AutomationControlled')\n", "    chrome_options.add_argument('--disable-infobars')\n", "    chrome_options.add_argument('--disable-dev-shm-usage')\n", "    chrome_options.add_argument('--disable-browser-side-navigation')\n", "    chrome_options.add_argument('--disable-gpu')\n", "    chrome_options.add_argument('--window-size=1000,700')\n", "    \n", "    # Create browser instance\n", "    main_browser = uc.Chrome(options=chrome_options)\n", "    main_browser.set_page_load_timeout(8)\n", "    \n", "    # Store first tab handle\n", "    tab_handles = [main_browser.current_window_handle]\n", "    \n", "    # Create additional tabs\n", "    for i in range(1, MAX_TABS):\n", "        main_browser.execute_script(\"window.open('', '_blank');\")\n", "        time.sleep(0.5)\n", "    \n", "    # Get all tab handles\n", "    tab_handles = main_browser.window_handles\n", "    print(f\"  ✅ Created browser with {len(tab_handles)} tabs\")\n", "    \n", "    # Reset request counter\n", "    total_requests = 0\n", "    \n", "    return main_browser\n", "\n", "# STEP 5: Worker function to process URLs using tabs\n", "def process_url_with_tab(tab_id, row_data, row_index):\n", "    global processed_count, total_requests\n", "    \n", "    verif_url = row_data['verif_url']\n", "    \n", "    # Check if we need to create/recreate browser\n", "    with browser_lock:\n", "        if (main_browser is None or \n", "            total_requests >= REQUESTS_PER_BROWSER):\n", "            \n", "            # Close existing browser if it exists\n", "            if main_browser is not None:\n", "                try:\n", "                    main_browser.quit()\n", "                    print(f\"  ♻ Closed browser after {total_requests} requests\")\n", "                except:\n", "                    pass\n", "            \n", "            # Create new browser with tabs\n", "            create_browser_with_tabs()\n", "        \n", "        # Increment total request count\n", "        total_requests += 1\n", "        current_request = total_requests\n", "        \n", "        # Get the tab handle for this worker\n", "        tab_handle = tab_handles[tab_id % len(tab_handles)]\n", "    \n", "    # Show progress\n", "    with progress_lock:\n", "        print(f\"Tab {tab_id}: Processing row {row_index + 1} - Request {current_request}/{REQUESTS_PER_BROWSER}\")\n", "    \n", "    # Initialize default values\n", "    website_url = None\n", "    siren_number = None\n", "    \n", "    try:\n", "        # Switch to the assigned tab\n", "        with browser_lock:\n", "            main_browser.switch_to.window(tab_handle)\n", "        \n", "        # Navigate to the URL\n", "        main_browser.get(verif_url)\n", "        \n", "        # Scroll down to trigger lazy loading\n", "        try:\n", "            main_browser.execute_script(\"window.scrollBy(0, 700);\")\n", "            time.sleep(0.5)\n", "        except:\n", "            pass\n", "        \n", "        # Try to find website URL\n", "        try:\n", "            element_wait = WebDriverWait(main_browser, 6)\n", "            element = element_wait.until(\n", "                EC.presence_of_element_located((By.XPATH, \"//div[span[text()='Site web']]//a\"))\n", "            )\n", "            website_url = element.get_attribute('href')\n", "        except:\n", "            pass\n", "        \n", "        # Extract SIREN number\n", "        try:\n", "            html = main_browser.page_source\n", "            selector = Selector(html)\n", "            siren_number = selector.xpath(\"//span[text()='SIREN (FR)']/following-sibling::div//span/text()\").get()\n", "        except:\n", "            pass\n", "            \n", "    except Exception as e:\n", "        with progress_lock:\n", "            print(f\"  ✗ Tab {tab_id}: Error processing row {row_index + 1}: {str(e)}\")\n", "    \n", "    # Prepare result row\n", "    result_row = row_data.copy()\n", "    result_row.update({\n", "        'website_url': website_url,\n", "        'siren_number': siren_number,\n", "    })\n", "    \n", "    # Save to CSV (thread-safe)\n", "    with csv_lock:\n", "        try:\n", "            with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:\n", "                writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "                writer.writerow(result_row)\n", "            processed_count += 1\n", "            with progress_lock:\n", "                print(f\"  ✓ Tab {tab_id}: Saved row {row_index + 1} - {website_url or 'No URL found'}\")\n", "        except Exception as e:\n", "            with progress_lock:\n", "                print(f\"  ✗ Tab {tab_id}: Error saving CSV: {str(e)}\")\n", "    \n", "    return result_row\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# STEP 6: Main execution - Parallel processing with single browser and multiple tabs\n", "print(f\"🚀 Starting parallel processing with 1 browser and {MAX_TABS} tabs...\")\n", "print(f\"📊 Browser processes {REQUESTS_PER_BROWSER} requests before restart\")\n", "print(f\"📁 Output: {OUTPUT_FILE}\")\n", "print(\"=\"*60)\n", "\n", "# Start timer\n", "start_time = time.time()\n", "\n", "# Prepare data for processing\n", "indexed_data = [(index, row) for index, row in enumerate(data_rows)]\n", "\n", "try:\n", "    # Execute parallel processing\n", "    with ThreadPoolExecutor(max_workers=MAX_TABS) as executor:\n", "        futures = []\n", "        \n", "        # Submit all jobs to thread pool\n", "        print(f\"📋 Submitting {len(indexed_data)} jobs to {MAX_TABS} tabs...\")\n", "        for i, (row_index, row_data) in enumerate(indexed_data):\n", "            tab_id = i % MAX_TABS  # Distribute work between tabs\n", "            future = executor.submit(process_url_with_tab, tab_id, row_data, row_index)\n", "            futures.append(future)\n", "        \n", "        print(\"⏳ Processing URLs...\")\n", "        \n", "        # Wait for all tasks to complete\n", "        completed = 0\n", "        for future in as_completed(futures):\n", "            try:\n", "                result = future.result()\n", "                completed += 1\n", "                \n", "                # Progress update every 10 completed items\n", "                if completed % 10 == 0:\n", "                    elapsed = time.time() - start_time\n", "                    rate = completed / elapsed * 60  # URLs per minute\n", "                    print(f\"📈 Progress: {completed}/{len(indexed_data)} ({completed/len(indexed_data)*100:.1f}%) | Rate: {rate:.1f} URLs/min\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"  ❌ Task failed: {str(e)}\")\n", "                completed += 1\n", "\n", "except KeyboardInterrupt:\n", "    print(\"\\n⚠️ Processing interrupted by user\")\n", "except Exception as e:\n", "    print(f\"\\n❌ Processing failed: {str(e)}\")\n", "\n", "finally:\n", "    # Clean up: Close browser\n", "    print(\"\\n🔄 Cleaning up browser...\")\n", "    if main_browser is not None:\n", "        try:\n", "            main_browser.quit()\n", "            print(\"  ✅ Closed main browser\")\n", "        except:\n", "            print(\"  ⚠️ Error closing main browser\")\n", "    \n", "    print(\"✅ Browser closed!\")\n", "\n", "# Final statistics\n", "end_time = time.time()\n", "total_time = end_time - start_time\n", "average_time = total_time / len(data_rows) if data_rows else 0\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"📊 PROCESSING COMPLETE!\")\n", "print(f\"⏱️  Total time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)\")\n", "print(f\"📋 Total processed: {processed_count} / {len(data_rows)} rows\")\n", "print(f\"⚡ Average per URL: {average_time:.2f} seconds\")\n", "print(f\"📁 Results saved to: {OUTPUT_FILE}\")\n", "print(\"=\"*60)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}