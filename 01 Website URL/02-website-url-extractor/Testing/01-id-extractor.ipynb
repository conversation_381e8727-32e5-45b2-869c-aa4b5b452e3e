{"cells": [{"cell_type": "code", "execution_count": 1, "id": "32d55272", "metadata": {}, "outputs": [], "source": ["from playwright.async_api import async_playwright\n", "from parsel import Selector\n", "import csv\n", "import requests\n", "import json\n", "import time\n", "import random\n", "from playwright_stealth import Stealth\n", "import re\n", "import os\n", "\n", "INPUT_FILE = \"extrait_10001_20001_part_2_output_id.csv\"\n", "OUTPUT_FILE = \"extrait_10001_20001_part_2_output_website_url.csv\"\n", "\n", "RAW_NUMBER = 0\n", "SKIP_RAW = 0"]}, {"cell_type": "code", "execution_count": 2, "id": "042070cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Response url='https://www.verif.com/societe/d411c14dbe031a97702e7692f723f0d36aef779c679eb7e56761b44a2cc38467/' request=<Request url='https://www.verif.com/societe/d411c14dbe031a97702e7692f723f0d36aef779c679eb7e56761b44a2cc38467/' method='GET'>>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["playwright = await async_playwright().start()\n", "\n", "# Launch browser without persistent context\n", "browser = await playwright.chromium.launch(\n", "    headless=False,\n", "    channel=\"chrome\",\n", "    args=[\n", "        \"--disable-blink-features=AutomationControlled\",\n", "        \"--exclude-switches=enable-automation\",\n", "        \"--disable-extensions\",\n", "        \"--disable-plugins-discovery\", \n", "        \"--disable-default-apps\",\n", "        \"--no-first-run\",\n", "        \"--disable-client-side-phishing-detection\",\n", "        \"--disable-popup-blocking\",\n", "        \"--disable-hang-monitor\",\n", "        \"--disable-sync\",\n", "        \"--no-report-upload\",\n", "        \"--disable-features=TranslateUI,VizDisplayCompositor\",\n", "        \"--disable-infobars\",\n", "        \"--disable-notifications\"\n", "    ]\n", ")\n", "\n", "# Create a new context with basic settings\n", "context = await browser.new_context(\n", "    user_agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) \"\n", "               \"AppleWebKit/537.36 (KHTML, like Gecko) \"\n", "               \"Chrome/131.0.0.0 Safari/537.36\",\n", "    locale=\"en-US\",\n", "    timezone_id=\"America/New_York\",\n", "    permissions=[\"geolocation\"],\n", "    viewport={\"width\":1366, \"height\":768}\n", ")\n", "\n", "page = await context.new_page()\n", "\n", "# Apply stealth configuration \n", "stealth = Stealth()\n", "await stealth.apply_stealth_async(page)\n", "\n", "await page.goto(\"https://www.verif.com/societe/d411c14dbe031a97702e7692f723f0d36aef779c679eb7e56761b44a2cc38467/\")"]}, {"cell_type": "code", "execution_count": null, "id": "f806fd34", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b91a8bb5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "197aacaa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "7b27b8a4", "metadata": {}, "outputs": [], "source": ["# Read the CSV file\n", "data_rows = []\n", "\n", "with open(INPUT_FILE, 'r', encoding='utf-8') as file:\n", "    csv_reader = csv.DictReader(file)\n", "    headers = csv_reader.fieldnames\n", "    \n", "    for row in csv_reader:\n", "        data_rows.append(row)"]}, {"cell_type": "code", "execution_count": 4, "id": "1f79f6ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing Raw Number: 0\n", "  Error processing URL https://www.verif.com/societe/d411c14dbe031a97702e7692f723f0d36aef779c679eb7e56761b44a2cc38467/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 1)\n", "Processing Raw Number: 1\n", "  Error processing URL https://www.verif.com/societe/c4febf78323b3dbae6a4c9646397e40e55aa1e700b555a15cf777aebaa02e135/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 2)\n", "Processing Raw Number: 2\n", "  Error processing URL https://www.verif.com/societe/f6a9df6f07d6c16e6efd7a5a16dd3438afeb2c138ba730940fc7c2cebbee7a8e/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 3)\n", "Processing Raw Number: 3\n", "  Error processing URL https://www.verif.com/societe/0f8d54c5b8c9f011bb095c16207181f4c6d41cd08a294564e311d087f8a95a2e/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 4)\n", "Processing Raw Number: 4\n", "  Error processing URL https://www.verif.com/societe/d91f74c3fd5d27c5c49186c5fe0b7ce8ab06471092365511351e48a116b356ad/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 5)\n", "Processing Raw Number: 5\n", "  Error processing URL https://www.verif.com/societe/53ec10e6bb47804c70251dec119246dbc1f72c14dc9a9dd490c831a8c1e3baad/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 6)\n", "Processing Raw Number: 6\n", "  Error processing URL https://www.verif.com/societe/e674c83fd48459f1c68ae8409a37b06d6b0a8348386a1ba2866325445fac4e57/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 7)\n", "Processing Raw Number: 7\n", "  Error processing URL https://www.verif.com/societe/7307a018595c54d51938b3c3bdf0688d67d80ce62d6ed5b984bab4895a3aaf3e/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 8)\n", "Processing Raw Number: 8\n", "  Error processing URL https://www.verif.com/societe/3d836cea9acb470b9424a921a528c076825bca2536f9b6f88bd958a3c3fad374/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 9)\n", "Processing Raw Number: 9\n", "  Error processing URL https://www.verif.com/societe/40b80f98ca05a39576f9439572e169c66062732ed46a0a8c93075a28c6927c90/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 10)\n", "Processing Raw Number: 10\n", "  Error processing URL https://www.verif.com/societe/d051fb5d03143dd4c539a498045f778e1f279c0fb4576c97da52c023ba06e468/: Page.wait_for_timeout() missing 1 required positional argument: 'timeout'\n", "  ✓ Saved to CSV (Total saved: 11)\n"]}, {"ename": "CancelledError", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mCancelledError\u001b[39m                            Trace<PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 80\u001b[39m\n\u001b[32m     77\u001b[39m         \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m  ✗ Error saving to CSV: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     79\u001b[39m     \u001b[38;5;66;03m# Add small delay between requests\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m80\u001b[39m     \u001b[38;5;28;01mawait\u001b[39;00m page.wait_for_timeout(random.randint(\u001b[32m100\u001b[39m, \u001b[32m500\u001b[39m))\n\u001b[32m     82\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mProcessing complete! Processed and saved \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprocessed_count\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m records to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mOUTPUT_FILE\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     84\u001b[39m \u001b[38;5;66;03m# Clean up\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/playwright/async_api/_generated.py:11408\u001b[39m, in \u001b[36mPage.wait_for_timeout\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m  11386\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwait_for_timeout\u001b[39m(\u001b[38;5;28mself\u001b[39m, timeout: \u001b[38;5;28mfloat\u001b[39m) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m  11387\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Page.wait_for_timeout\u001b[39;00m\n\u001b[32m  11388\u001b[39m \n\u001b[32m  11389\u001b[39m \u001b[33;03m    Waits for the given `timeout` in milliseconds.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m  11404\u001b[39m \u001b[33;03m        A timeout to wait for\u001b[39;00m\n\u001b[32m  11405\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m  11407\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m mapping.from_maybe_impl(\n\u001b[32m> \u001b[39m\u001b[32m11408\u001b[39m         \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m._impl_obj.wait_for_timeout(timeout=timeout)\n\u001b[32m  11409\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/playwright/_impl/_page.py:1079\u001b[39m, in \u001b[36mPage.wait_for_timeout\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m   1078\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwait_for_timeout\u001b[39m(\u001b[38;5;28mself\u001b[39m, timeout: \u001b[38;5;28mfloat\u001b[39m) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1079\u001b[39m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m._main_frame.wait_for_timeout(timeout)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/playwright/_impl/_frame.py:756\u001b[39m, in \u001b[36mFrame.wait_for_timeout\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    755\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwait_for_timeout\u001b[39m(\u001b[38;5;28mself\u001b[39m, timeout: \u001b[38;5;28mfloat\u001b[39m) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m756\u001b[39m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m._channel.send(\u001b[33m\"\u001b[39m\u001b[33mwaitForTimeout\u001b[39m\u001b[33m\"\u001b[39m, locals_to_params(\u001b[38;5;28mlocals\u001b[39m()))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/playwright/_impl/_connection.py:61\u001b[39m, in \u001b[36mChannel.send\u001b[39m\u001b[34m(self, method, params)\u001b[39m\n\u001b[32m     60\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34msend\u001b[39m(\u001b[38;5;28mself\u001b[39m, method: \u001b[38;5;28mstr\u001b[39m, params: Dict = \u001b[38;5;28;01mNone\u001b[39;00m) -> Any:\n\u001b[32m---> \u001b[39m\u001b[32m61\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection.wrap_api_call(\n\u001b[32m     62\u001b[39m         \u001b[38;5;28;01mlambda\u001b[39;00m: \u001b[38;5;28mself\u001b[39m._inner_send(method, params, \u001b[38;5;28;01mFalse\u001b[39;00m),\n\u001b[32m     63\u001b[39m         \u001b[38;5;28mself\u001b[39m._is_internal_type,\n\u001b[32m     64\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/playwright/_impl/_connection.py:526\u001b[39m, in \u001b[36mConnection.wrap_api_call\u001b[39m\u001b[34m(self, cb, is_internal)\u001b[39m\n\u001b[32m    524\u001b[39m \u001b[38;5;28mself\u001b[39m._api_zone.set(parsed_st)\n\u001b[32m    525\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m526\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m cb()\n\u001b[32m    527\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m error:\n\u001b[32m    528\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m rewrite_error(error, \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mparsed_st[\u001b[33m'\u001b[39m\u001b[33mapiName\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00<PERSON><PERSON>r\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/playwright/_impl/_connection.py:92\u001b[39m, in \u001b[36mChannel._inner_send\u001b[39m\u001b[34m(self, method, params, return_as_dict)\u001b[39m\n\u001b[32m     88\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m error\n\u001b[32m     89\u001b[39m callback = \u001b[38;5;28mself\u001b[39m._connection._send_message_to_server(\n\u001b[32m     90\u001b[39m     \u001b[38;5;28mself\u001b[39m._object, method, _filter_none(params)\n\u001b[32m     91\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m92\u001b[39m done, _ = \u001b[38;5;28;01mawait\u001b[39;00m asyncio.wait(\n\u001b[32m     93\u001b[39m     {\n\u001b[32m     94\u001b[39m         \u001b[38;5;28mself\u001b[39m._connection._transport.on_error_future,\n\u001b[32m     95\u001b[39m         callback.future,\n\u001b[32m     96\u001b[39m     },\n\u001b[32m     97\u001b[39m     return_when=asyncio.FIRST_COMPLETED,\n\u001b[32m     98\u001b[39m )\n\u001b[32m     99\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m callback.future.done():\n\u001b[32m    100\u001b[39m     callback.future.cancel()\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/asyncio/tasks.py:464\u001b[39m, in \u001b[36mwait\u001b[39m\u001b[34m(fs, timeout, return_when)\u001b[39m\n\u001b[32m    461\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mPassing coroutines is forbidden, use tasks explicitly.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    463\u001b[39m loop = events.get_running_loop()\n\u001b[32m--> \u001b[39m\u001b[32m464\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m _wait(fs, timeout, return_when, loop)\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/asyncio/tasks.py:550\u001b[39m, in \u001b[36m_wait\u001b[39m\u001b[34m(fs, timeout, return_when, loop)\u001b[39m\n\u001b[32m    547\u001b[39m     f.add_done_callback(_on_completion)\n\u001b[32m    549\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m550\u001b[39m     \u001b[38;5;28;01mawait\u001b[39;00m waiter\n\u001b[32m    551\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    552\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m timeout_handle \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[31mCancelledError\u001b[39m: "]}], "source": ["# Prepare CSV file with headers (create/overwrite the file)\n", "original_fieldnames = list(data_rows[0].keys()) if data_rows else []\n", "new_fieldnames = [\"website_url\", \"siren_number\"]\n", "all_fieldnames = original_fieldnames + new_fieldnames\n", "\n", "# Create CSV file and write header\n", "with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "    writer.writeheader()\n", "\n", "\n", "# Process each row and save immediately\n", "processed_count = 0\n", "for index, row in enumerate(data_rows):\n", "    if index < SKIP_RAW:\n", "        continue\n", "\n", "    verif_url = row['verif_url']\n", "    current_row = index + 1\n", "    \n", "    # Dynamic progress display\n", "    print(f\"Processing Raw Number: {RAW_NUMBER}\")\n", "    RAW_NUMBER += 1\n", "    \n", "    # Initialize default values\n", "    website_url = None\n", "    siren_number = None\n", "    \n", "    try:\n", "        await page.goto(verif_url)\n", "        await page.wait_for_timeout()\n", "\n", "        # Scroll down to trigger lazy loading\n", "        try:\n", "            await page.evaluate(\"window.scrollBy(0, 700);\")\n", "            await page.wait_for_timeout(500)\n", "        except:\n", "            pass\n", "\n", "        # Try to find website URL\n", "        try:\n", "            website_element = await page.wait_for_selector(\"//div[span[text()='Site web']]//a\", timeout=6000)\n", "            if website_element:\n", "                website_url = await website_element.get_attribute('href')\n", "        except:\n", "            pass\n", "        \n", "        # Extract SIREN number\n", "        try:\n", "            siren_element = await page.locator(\"//span[text()='SIREN (FR)']/following-sibling::div//span\").first\n", "            if siren_element:\n", "                siren_number = await siren_element.text_content()\n", "        except:\n", "            pass\n", "\n", "        print(f\"  Website URL: {website_url}\")\n", "        print(f\"  SIREN: {siren_number}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"  Error processing URL {verif_url}: {str(e)}\")\n", "    \n", "    # Prepare the result row (combining original row data with new extracted data)\n", "    result_row = row.copy()\n", "    result_row.update({\n", "        'website_url': website_url,\n", "        'siren_number': siren_number\n", "    })\n", "    \n", "    # Save immediately to CSV file (append mode)\n", "    try:\n", "        with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:\n", "            writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "            writer.writerow(result_row)\n", "        processed_count += 1\n", "        print(f\"  ✓ Saved to CSV (Total saved: {processed_count})\")\n", "    except Exception as e:\n", "        print(f\"  ✗ Error saving to CSV: {str(e)}\")\n", "    \n", "    # Add small delay between requests\n", "    await page.wait_for_timeout(random.randint(100, 500))\n", "\n", "print(f\"\\nProcessing complete! Processed and saved {processed_count} records to {OUTPUT_FILE}.\")\n", "\n", "# Clean up\n", "await context.close()\n", "await browser.close()\n", "await playwright.stop()"]}, {"cell_type": "code", "execution_count": null, "id": "48d81adc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8653a264", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "25dd1161", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a84221d5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fa42fdd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4fdc5441", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "aea4e5b9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc59a5ce", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}