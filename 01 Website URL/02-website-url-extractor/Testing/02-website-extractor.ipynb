{"cells": [{"cell_type": "code", "execution_count": 1, "id": "442717f6", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "import csv\n", "import time\n", "from urllib.parse import urlparse\n", "from parsel import Selector\n", "import undetected_chromedriver as uc\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "INPUT_FILE = \"extrait_10001_20001_part_2_output_id.csv\"\n", "OUTPUT_FILE = \"extrait_10001_20001_part_2_output_url.csv\"\n", "user_data_dir = os.path.join(os.getcwd(), \"aliexpress_login_files_2\")\n", "RAW_NUMBER = 0\n", "SKIP_RAW = 0"]}, {"cell_type": "code", "execution_count": null, "id": "161f030a", "metadata": {}, "outputs": [], "source": ["\n", "chrome_options = uc.ChromeOptions()\n", "chrome_options.add_argument(f\"--user-data-dir={user_data_dir}\")\n", "options = [\n", "    '--no-sandbox',\n", "    '--start-maximized',\n", "    '--disable-extensions',\n", "    '--ignore-certificate-errors',\n", "    '--disable-blink-features=AutomationControlled',\n", "    '--disable-infobars',\n", "    '--disable-dev-shm-usage',\n", "    '--disable-browser-side-navigation',\n", "    '--disable-gpu'\n", "]\n", "\n", "for option in options:\n", "    chrome_options.add_argument(option)\n", "\n", "driver = uc.Chrome(options=chrome_options)\n", "\n", "# Set page load timeout to 3 seconds\n", "driver.set_page_load_timeout(3)\n", "\n", "# Initialize WebDriverWait with a 30-second timeout\n", "driver_wait = WebDriverWait(driver, 10)"]}, {"cell_type": "code", "execution_count": 3, "id": "04365c7f", "metadata": {}, "outputs": [], "source": ["# Read the CSV file\n", "data_rows = []\n", "\n", "with open(INPUT_FILE, 'r', encoding='utf-8') as file:\n", "    csv_reader = csv.DictReader(file)\n", "    headers = csv_reader.fieldnames\n", "    \n", "    for row in csv_reader:\n", "        data_rows.append(row)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "aedd562b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing Raw Number: 1 ...\n"]}, {"ename": "MaxRetryError", "evalue": "HTTPConnectionPool(host='localhost', port=60571): Max retries exceeded with url: /session/85586cc33496bc869e11f2fee5936eba/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7b7ba8bea8a0>: Failed to establish a new connection: [Errno 111] Connection refused'))", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mConnectionRefusedError\u001b[39m                    <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connection.py:198\u001b[39m, in \u001b[36mHTTPConnection._new_conn\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    197\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m198\u001b[39m     sock = \u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate_connection\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    199\u001b[39m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_dns_host\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mport\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    200\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    201\u001b[39m \u001b[43m        \u001b[49m\u001b[43msource_address\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msource_address\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    202\u001b[39m \u001b[43m        \u001b[49m\u001b[43msocket_options\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msocket_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    203\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    204\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m socket.gaierror \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/util/connection.py:85\u001b[39m, in \u001b[36mcreate_connection\u001b[39m\u001b[34m(address, timeout, source_address, socket_options)\u001b[39m\n\u001b[32m     84\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m85\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m err\n\u001b[32m     86\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m     87\u001b[39m     \u001b[38;5;66;03m# Break explicitly a reference cycle\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/util/connection.py:73\u001b[39m, in \u001b[36mcreate_connection\u001b[39m\u001b[34m(address, timeout, source_address, socket_options)\u001b[39m\n\u001b[32m     72\u001b[39m     sock.bind(source_address)\n\u001b[32m---> \u001b[39m\u001b[32m73\u001b[39m \u001b[43msock\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43msa\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     74\u001b[39m \u001b[38;5;66;03m# Break explicitly a reference cycle\u001b[39;00m\n", "\u001b[31mConnectionRefusedError\u001b[39m: [Errno 111] Connection refused", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mNewConnectionError\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connectionpool.py:787\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    786\u001b[39m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m787\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    788\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    789\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    790\u001b[39m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    791\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    792\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    793\u001b[39m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    794\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    795\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    797\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    798\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    799\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    800\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    802\u001b[39m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connectionpool.py:493\u001b[39m, in \u001b[36mHTTPConnectionPool._make_request\u001b[39m\u001b[34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    492\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m493\u001b[39m     \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    494\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    495\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    496\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    497\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    498\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    499\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    500\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    501\u001b[39m \u001b[43m        \u001b[49m\u001b[43menforce_content_length\u001b[49m\u001b[43m=\u001b[49m\u001b[43menforce_content_length\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    502\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    504\u001b[39m \u001b[38;5;66;03m# We are swallowing BrokenPipeError (errno.EPIPE) since the server is\u001b[39;00m\n\u001b[32m    505\u001b[39m \u001b[38;5;66;03m# legitimately able to close the connection after sending a valid response.\u001b[39;00m\n\u001b[32m    506\u001b[39m \u001b[38;5;66;03m# With this behaviour, the received response is still readable.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connection.py:445\u001b[39m, in \u001b[36mHTTPConnection.request\u001b[39m\u001b[34m(self, method, url, body, headers, chunked, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    444\u001b[39m     \u001b[38;5;28mself\u001b[39m.putheader(header, value)\n\u001b[32m--> \u001b[39m\u001b[32m445\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mendheaders\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    447\u001b[39m \u001b[38;5;66;03m# If we're given a body we start sending that in chunks.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/http/client.py:1331\u001b[39m, in \u001b[36mHTTPConnection.endheaders\u001b[39m\u001b[34m(self, message_body, encode_chunked)\u001b[39m\n\u001b[32m   1330\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m CannotSendHeader()\n\u001b[32m-> \u001b[39m\u001b[32m1331\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_output\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessage_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/http/client.py:1091\u001b[39m, in \u001b[36mHTTPConnection._send_output\u001b[39m\u001b[34m(self, message_body, encode_chunked)\u001b[39m\n\u001b[32m   1090\u001b[39m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m._buffer[:]\n\u001b[32m-> \u001b[39m\u001b[32m1091\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmsg\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1093\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m message_body \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   1094\u001b[39m \n\u001b[32m   1095\u001b[39m     \u001b[38;5;66;03m# create a consistent interface to message_body\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/usr/lib/python3.12/http/client.py:1035\u001b[39m, in \u001b[36mHTTPConnection.send\u001b[39m\u001b[34m(self, data)\u001b[39m\n\u001b[32m   1034\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.auto_open:\n\u001b[32m-> \u001b[39m\u001b[32m1035\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1036\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connection.py:276\u001b[39m, in \u001b[36mHTTPConnection.connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    275\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m276\u001b[39m     \u001b[38;5;28mself\u001b[39m.sock = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    277\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._tunnel_host:\n\u001b[32m    278\u001b[39m         \u001b[38;5;66;03m# If we're tunneling it means we're connected to our proxy.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connection.py:213\u001b[39m, in \u001b[36mHTTPConnection._new_conn\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    212\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>rror\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m213\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m NewConnectionError(\n\u001b[32m    214\u001b[39m         \u001b[38;5;28mself\u001b[39m, \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFailed to establish a new connection: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    215\u001b[39m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m    217\u001b[39m sys.audit(\u001b[33m\"\u001b[39m\u001b[33mhttp.client.connect\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28mself\u001b[39m, \u001b[38;5;28mself\u001b[39m.host, \u001b[38;5;28mself\u001b[39m.port)\n", "\u001b[31mNewConnectionError\u001b[39m: <urllib3.connection.HTTPConnection object at 0x7b7ba8bea8a0>: Failed to establish a new connection: [Errno 111] Connection refused", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mMaxRetryError\u001b[39m                             <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 41\u001b[39m\n\u001b[32m     37\u001b[39m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m     40\u001b[39m \u001b[38;5;66;03m# Scroll down a little bit to trigger any lazy loading\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m41\u001b[39m \u001b[43mdriver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute_script\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mwindow.scrollBy(0, 700);\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     42\u001b[39m time.sleep(\u001b[32m.3\u001b[39m)  \u001b[38;5;66;03m# Give a moment for content to load after scroll\u001b[39;00m\n\u001b[32m     45\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:528\u001b[39m, in \u001b[36mWebDriver.execute_script\u001b[39m\u001b[34m(self, script, *args)\u001b[39m\n\u001b[32m    525\u001b[39m converted_args = \u001b[38;5;28mlist\u001b[39m(args)\n\u001b[32m    526\u001b[39m command = Command.W3C_EXECUTE_SCRIPT\n\u001b[32m--> \u001b[39m\u001b[32m528\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mscript\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mscript\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43margs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mconverted_args\u001b[49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m[\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:427\u001b[39m, in \u001b[36mWebDriver.execute\u001b[39m\u001b[34m(self, driver_command, params)\u001b[39m\n\u001b[32m    424\u001b[39m     \u001b[38;5;28;01melif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33msessionId\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m params:\n\u001b[32m    425\u001b[39m         params[\u001b[33m\"\u001b[39m\u001b[33msessionId\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28mself\u001b[39m.session_id\n\u001b[32m--> \u001b[39m\u001b[32m427\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcommand_executor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdriver_command\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    428\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[32m    429\u001b[39m     \u001b[38;5;28mself\u001b[39m.error_handler.check_response(response)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/selenium/webdriver/remote/remote_connection.py:404\u001b[39m, in \u001b[36mRemoteConnection.execute\u001b[39m\u001b[34m(self, command, params)\u001b[39m\n\u001b[32m    402\u001b[39m trimmed = \u001b[38;5;28mself\u001b[39m._trim_large_entries(params)\n\u001b[32m    403\u001b[39m LOGGER.debug(\u001b[33m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, command_info[\u001b[32m0\u001b[39m], url, \u001b[38;5;28mstr\u001b[39m(trimmed))\n\u001b[32m--> \u001b[39m\u001b[32m404\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand_info\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/selenium/webdriver/remote/remote_connection.py:428\u001b[39m, in \u001b[36mRemoteConnection._request\u001b[39m\u001b[34m(self, method, url, body)\u001b[39m\n\u001b[32m    425\u001b[39m     body = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    427\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._client_config.keep_alive:\n\u001b[32m--> \u001b[39m\u001b[32m428\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_conn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_client_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    429\u001b[39m     statuscode = response.status\n\u001b[32m    430\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/_request_methods.py:143\u001b[39m, in \u001b[36mRequestMethods.request\u001b[39m\u001b[34m(self, method, url, body, fields, headers, json, **urlopen_kw)\u001b[39m\n\u001b[32m    135\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.request_encode_url(\n\u001b[32m    136\u001b[39m         method,\n\u001b[32m    137\u001b[39m         url,\n\u001b[32m   (...)\u001b[39m\u001b[32m    140\u001b[39m         **urlopen_kw,\n\u001b[32m    141\u001b[39m     )\n\u001b[32m    142\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m143\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest_encode_body\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    144\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfields\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfields\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43murlopen_kw\u001b[49m\n\u001b[32m    145\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/_request_methods.py:278\u001b[39m, in \u001b[36mRequestMethods.request_encode_body\u001b[39m\u001b[34m(self, method, url, fields, headers, encode_multipart, multipart_boundary, **urlopen_kw)\u001b[39m\n\u001b[32m    274\u001b[39m     extra_kw[\u001b[33m\"\u001b[39m\u001b[33mheaders\u001b[39m\u001b[33m\"\u001b[39m].setdefault(\u001b[33m\"\u001b[39m\u001b[33mContent-Type\u001b[39m\u001b[33m\"\u001b[39m, content_type)\n\u001b[32m    276\u001b[39m extra_kw.update(urlopen_kw)\n\u001b[32m--> \u001b[39m\u001b[32m278\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mextra_kw\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/poolmanager.py:443\u001b[39m, in \u001b[36mPoolManager.urlopen\u001b[39m\u001b[34m(self, method, url, redirect, **kw)\u001b[39m\n\u001b[32m    441\u001b[39m     response = conn.urlopen(method, url, **kw)\n\u001b[32m    442\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m443\u001b[39m     response = \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest_uri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    445\u001b[39m redirect_location = redirect \u001b[38;5;129;01mand\u001b[39;00m response.get_redirect_location()\n\u001b[32m    446\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m redirect_location:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connectionpool.py:871\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    866\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m conn:\n\u001b[32m    867\u001b[39m     \u001b[38;5;66;03m# Try again\u001b[39;00m\n\u001b[32m    868\u001b[39m     log.warning(\n\u001b[32m    869\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m) after connection broken by \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, retries, err, url\n\u001b[32m    870\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m871\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    872\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    873\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    880\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_timeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    881\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrelease_conn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrelease_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    882\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    883\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody_pos\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody_pos\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    884\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    885\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    886\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    887\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    889\u001b[39m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n\u001b[32m    890\u001b[39m redirect_location = redirect \u001b[38;5;129;01mand\u001b[39;00m response.get_redirect_location()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connectionpool.py:871\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    866\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m conn:\n\u001b[32m    867\u001b[39m     \u001b[38;5;66;03m# Try again\u001b[39;00m\n\u001b[32m    868\u001b[39m     log.warning(\n\u001b[32m    869\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m) after connection broken by \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, retries, err, url\n\u001b[32m    870\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m871\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    872\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    873\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    880\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_timeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    881\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrelease_conn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrelease_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    882\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    883\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody_pos\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody_pos\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    884\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    885\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    886\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    887\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    889\u001b[39m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n\u001b[32m    890\u001b[39m redirect_location = redirect \u001b[38;5;129;01mand\u001b[39;00m response.get_redirect_location()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connectionpool.py:871\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    866\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m conn:\n\u001b[32m    867\u001b[39m     \u001b[38;5;66;03m# Try again\u001b[39;00m\n\u001b[32m    868\u001b[39m     log.warning(\n\u001b[32m    869\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m) after connection broken by \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, retries, err, url\n\u001b[32m    870\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m871\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    872\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    873\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    880\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_timeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    881\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrelease_conn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrelease_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    882\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    883\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody_pos\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody_pos\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    884\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    885\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    886\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    887\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    889\u001b[39m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n\u001b[32m    890\u001b[39m redirect_location = redirect \u001b[38;5;129;01mand\u001b[39;00m response.get_redirect_location()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/connectionpool.py:841\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    838\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(new_e, (\u001b[38;5;167;01mOSError\u001b[39;00m, HTTPException)):\n\u001b[32m    839\u001b[39m     new_e = ProtocolError(\u001b[33m\"\u001b[39m\u001b[33mConnection aborted.\u001b[39m\u001b[33m\"\u001b[39m, new_e)\n\u001b[32m--> \u001b[39m\u001b[32m841\u001b[39m retries = \u001b[43mretries\u001b[49m\u001b[43m.\u001b[49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    842\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnew_e\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[43m=\u001b[49m\u001b[43msys\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[32m    843\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    844\u001b[39m retries.sleep()\n\u001b[32m    846\u001b[39m \u001b[38;5;66;03m# Keep track of the error for the retry warning.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/env/lib/python3.12/site-packages/urllib3/util/retry.py:519\u001b[39m, in \u001b[36mRetry.increment\u001b[39m\u001b[34m(self, method, url, response, error, _pool, _stacktrace)\u001b[39m\n\u001b[32m    517\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m new_retry.is_exhausted():\n\u001b[32m    518\u001b[39m     reason = error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause)\n\u001b[32m--> \u001b[39m\u001b[32m519\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, reason) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mreason\u001b[39;00m  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[32m    521\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mIncremented Retry for (url=\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m\"\u001b[39m, url, new_retry)\n\u001b[32m    523\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m new_retry\n", "\u001b[31mMaxRetryError\u001b[39m: HTTPConnectionPool(host='localhost', port=60571): Max retries exceeded with url: /session/85586cc33496bc869e11f2fee5936eba/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7b7ba8bea8a0>: Failed to establish a new connection: [Errno 111] Connection refused'))"]}], "source": ["# Prepare CSV file with headers (create/overwrite the file)\n", "original_fieldnames = list(data_rows[0].keys()) if data_rows else []\n", "new_fieldnames = ['website_url', 'siren_number']\n", "all_fieldnames = original_fieldnames + new_fieldnames\n", "\n", "# Create CSV file and write header\n", "with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "    writer.writeheader()\n", "\n", "\n", "# Process each row and save immediately\n", "processed_count = 0\n", "for index, row in enumerate(data_rows):\n", "    if index < SKIP_RAW:\n", "        continue\n", "\n", "    verif_url = row['verif_url']\n", "    current_row = index + 1\n", "    \n", "\n", "    # Dynamic progress display\n", "    print(f\"Processing Raw Number: {RAW_NUMBER} ...\")\n", "    RAW_NUMBER += 1\n", "    \n", "\n", "\n", "    # Initialize default values\n", "    website_url = None\n", "    siren_number = None\n", "    \n", "\n", "    try:\n", "        # Navigate to the URL\n", "        driver.get(verif_url)\n", "    except:\n", "        pass\n", "    \n", "\n", "    # Scroll down a little bit to trigger any lazy loading\n", "    driver.execute_script(\"window.scrollBy(0, 700);\")\n", "    time.sleep(.3)  # Give a moment for content to load after scroll\n", "        \n", "    \n", "    try:\n", "        element_wait = WebDriverWait(driver, 3)  # 7-second timeout for this specific element\n", "        element = element_wait.until(\n", "            EC.presence_of_element_located((By.XPATH, \"//div[span[text()='Site web']]//a\"))\n", "        )\n", "\n", "        # If element is found, get its href attribute\n", "        website_url = element.get_attribute('href')\n", "    except:\n", "        pass\n", "    \n", "\n", "    html = driver.page_source\n", "    selector = Selector(html)\n", "    siren_number = selector.xpath(\"//span[text()='SIREN (FR)']/following-sibling::div//span/text()\").get()\n", "\n", "\n", "    \n", "    # Prepare the result row (combining original row data with new extracted data)\n", "    result_row = row.copy()\n", "    result_row.update({\n", "        'website_url': website_url,\n", "        'siren_number': siren_number,\n", "    })\n", "    \n", "    \n", "    # Save immediately to CSV file (append mode)\n", "    try:\n", "        with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:\n", "            writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "            writer.writerow(result_row)\n", "        processed_count += 1\n", "    except Exception as e:\n", "        print(f\"  ✗ Error saving to CSV: {str(e)}\")\n", "\n", "print(f\"\\nProcessing complete! Processed and saved {processed_count} records to {OUTPUT_FILE}.\")    "]}, {"cell_type": "code", "execution_count": null, "id": "ef1c2fe4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "594b3a4b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}