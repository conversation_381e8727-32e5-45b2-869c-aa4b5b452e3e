{"cells": [{"cell_type": "code", "execution_count": null, "id": "e474cc6f", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "import csv\n", "import time\n", "from urllib.parse import urlparse\n", "from parsel import Selector\n", "import undetected_chromedriver as uc\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "from selenium.common.exceptions import TimeoutException"]}, {"cell_type": "code", "execution_count": null, "id": "d673510c", "metadata": {}, "outputs": [], "source": ["chrome_options = uc.ChromeOptions()\n", "options = [\n", "    '--no-sandbox',\n", "    '--start-maximized',\n", "    '--disable-extensions',\n", "    '--ignore-certificate-errors',\n", "    '--disable-blink-features=AutomationControlled',\n", "    '--disable-infobars',\n", "    '--disable-dev-shm-usage',\n", "    '--disable-browser-side-navigation',\n", "    '--disable-gpu'\n", "]\n", "\n", "for option in options:\n", "    chrome_options.add_argument(option)\n", "\n", "driver = uc.Chrome(options=chrome_options)\n", "\n", "# Set page load timeout to 3 seconds\n", "driver.set_page_load_timeout(5)\n", "\n", "# Initialize WebDriverWait with a 30-second timeout\n", "driver_wait = WebDriverWait(driver, 30)"]}, {"cell_type": "code", "execution_count": null, "id": "7ce33b0a", "metadata": {}, "outputs": [], "source": ["INPUT_FILE = \"extrait_10001_20001_part_1.csv\"\n", "OUTPUT_FILE = \"part-1-data.csv\"\n", "\n", "RAW_NUMBER = 0\n", "SKIP_RAW = 94  # Start from beginning\n", "\n", "# Read the CSV file\n", "data_rows = []\n", "\n", "with open(INPUT_FILE, 'r', encoding='utf-8') as file:\n", "    csv_reader = csv.DictReader(file)\n", "    headers = csv_reader.fieldnames\n", "    \n", "    for row in csv_reader:\n", "        data_rows.append(row)"]}, {"cell_type": "code", "execution_count": null, "id": "b7bd342c", "metadata": {}, "outputs": [], "source": ["# Function to detect captcha\n", "def detect_captcha(page_source):\n", "    \"\"\"\n", "    Detect if the current page contains a captcha or security check\n", "    Only triggers on the specific captcha message\n", "    \"\"\"\n", "    # Only look for the specific captcha message you mentioned\n", "    captcha_text = \"Let's confirm you are human\"\n", "    \n", "    if captcha_text in page_source:\n", "        return True\n", "    return False\n", "\n", "def handle_captcha_manual():\n", "    \"\"\"\n", "    Handle cap<PERSON>a by pausing and waiting for manual intervention\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🚨 CAPTCHA DETECTED! 🚨\")\n", "    print(\"=\"*60)\n", "    print(\"A captcha or security check has been detected.\")\n", "    print(\"Please solve the captcha manually in the browser window.\")\n", "    print(\"After solving the captcha, press ENTER to continue...\")\n", "    print(\"=\"*60)\n", "    \n", "    # Wait for user input\n", "    input(\"Press ENTER after solving the captcha: \")\n", "    \n", "    print(\"✅ Resuming script...\")\n", "    print(\"=\"*60 + \"\\n\")\n", "\n", "# Prepare CSV file with headers (create/overwrite the file)\n", "original_fieldnames = list(data_rows[0].keys()) if data_rows else []\n", "new_fieldnames = ['website', 'phone', \"email\"]\n", "all_fieldnames = original_fieldnames + new_fieldnames\n", "\n", "# Create CSV file and write header\n", "with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "    writer.writeheader()\n", "\n", "\n", "# Process each row and save immediately\n", "processed_count = 0\n", "for index, row in enumerate(data_rows):\n", "    if index < SKIP_RAW:\n", "        continue\n", "\n", "    siret = row['siret']\n", "    denominationUniteLegale = row['denominationUniteLegale']\n", "    verif_url = f\"https://infonet.fr/entreprises/{siret}-{denominationUniteLegale.replace(' ', '-')}\"\n", "    \n", "    \n", "    \n", "    \n", "    \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "    \n", "    current_row = index + 1\n", "    print(f\"Processing Raw Number: {RAW_NUMBER} ...\")\n", "    RAW_NUMBER += 1\n", "    \n", "\n", "    # Initialize default values\n", "    website = None\n", "    phone = None\n", "    email = None\n", "    \n", "\n", "    try:\n", "        # Navigate to the URL\n", "        driver.get(verif_url)\n", "    except:\n", "        pass\n", "\n", "    time.sleep(1)\n", "\n", "    # Get page source for captcha detection\n", "    html = driver.page_source\n", "    \n", "    # Check for cap<PERSON><PERSON> before proceeding\n", "    if detect_captcha(html):\n", "        handle_captcha_manual()\n", "        # Get fresh page source after cap<PERSON><PERSON> is solved\n", "        time.sleep(2)\n", "        html = driver.page_source\n", "    \n", "    selector = Selector(html)\n", "\n", "    phone = selector.xpath(\"//span[@id='header-company-phone']//a[starts-with(@href, 'tel:')]/text()\").get()\n", "    email = selector.xpath(\"//span[@id='header-company-email']//a[starts-with(@href, 'mailto:')]/text()\").get()\n", "    website = selector.xpath(\"//span[@id='header-company-website']//a/@href\").get()\n", "\n", "    # Prepare the result row (combining original row data with new extracted data)\n", "    result_row = row.copy()\n", "    result_row.update({\n", "        'website': website,\n", "        'phone': phone,\n", "        \"email\": email\n", "    })\n", "    \n", "    \n", "    # Save immediately to CSV file (append mode)\n", "    try:\n", "        with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:\n", "            writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)\n", "            writer.writerow(result_row)\n", "        processed_count += 1\n", "    except Exception as e:\n", "        print(f\"  ✗ Error saving to CSV: {str(e)}\")\n", "\n", "print(f\"\\nProcessing complete! Processed and saved {processed_count} records to {OUTPUT_FILE}.\")  "]}, {"cell_type": "code", "execution_count": null, "id": "4e2ec2ca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "932231e0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}